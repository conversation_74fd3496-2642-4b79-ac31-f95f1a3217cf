# WhatsApp Spotify Downloader Bot (Converted from Telegram)

## How to use as the client
Send the bot a link from Spotify via WhatsApp and it'll download it for you.
  - It can be a track link like this:
https://open.spotify.com/track/734dz1YaFITwawPpM25fSt
  - Or an album like this:
https://open.spotify.com/album/0Lg1uZvI312TPqxNWShFXL
  - Or a playlist like this:
https://open.spotify.com/playlist/37i9dQZF1DWX4UlFW6EJPs

**Note**: Files are sent as documents and are limited to 100MB (WhatsApp limit).

## How to deploy as the developer (Ubuntu VPS guide)

### Prerequisites
- Ubuntu VPS with SSH access (headless)
- Node.js 16+ and npm
- Python 3.8+
- Git

### Installation Steps

1. **General pre-install updates:**
```bash
sudo apt update
sudo apt upgrade
sudo apt-get update
sudo apt-get upgrade
```

2. **Install Node.js and npm:**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

3. **Clone the repository:**
```bash
git clone https://github.com/arashnm80/spot-seek-bot.git
cd spot-seek-bot
```

4. **Install Python dependencies:**
```bash
sudo apt install python3-pip
pip install -r requirements.txt
```

5. **Install Node.js dependencies:**
```bash
npm install
```

6. **Install ffmpeg:**
```bash
sudo apt install ffmpeg
```

7. **Setup proxychains4 (optional for warp):**
```bash
sudo apt-get install proxychains4
# Configure warp proxy in /etc/proxychains4.conf if needed
```

8. **Configure environment variables in `variables.py`:**
   - `warp_mode` - set to `False` if you don't have warp socks proxy
   - `spotify_apps_list` - list of Spotify app IDs and secrets from [developer.spotify.com](https://developer.spotify.com/)

9. **Make scripts executable:**
```bash
chmod +x start_whatsapp_bot.sh stop_whatsapp_bot.sh
```

10. **Create database:**
    - Create `music.db` from scratch via `create_database()` or use existing one

### Running the Bot

**Start the bot:**
```bash
./start_whatsapp_bot.sh
```

**Stop the bot:**
```bash
./stop_whatsapp_bot.sh
```

**For production (systemd service):**
Create a systemd service file for automatic startup and management.

### First Time Setup
1. Run the bot for the first time
2. Scan the QR code with your WhatsApp to authenticate
3. The bot will create persistent session data in `./wwebjs_auth_session/`
4. After initial setup, the bot will run headlessly without requiring QR scans

## Technical info about how this WhatsApp bot works
- When you send a Spotify link to the bot via WhatsApp, it searches through its database. If it's the first time it sees this link, it will download it with spotdl. If it has been downloaded before, it saves time by using previously downloaded files from the database.
- The bot uses a queue system to handle multiple requests efficiently
- Files are sent as WhatsApp documents with a 100MB size limit
- The bot uses whatsapp-web.js with persistent authentication (LocalAuth) to avoid repeated QR code scans
- We use Spotify API to get tracks from a valid link, so you should sign up at https://developer.spotify.com/ and get your own token
- All mp3 files are downloaded with high 320k quality
- The bot runs headlessly on Ubuntu VPS without requiring a GUI

## csv files columns guide
- Note: starting template of each csv should be headers and **one empty new line** after them
### database csv columns
`date and time added` | `spotify track id` | `telegram audio id`
### users csv columns
`unique user id` | `last use date and time`

## TO-DO: ideas & bugs to fix & features to add
- [ ] support searching name of song by user
- [ ] support inline mode
- [ ] option to enable or disable warp mode for everything (spotipy, spotdl, ...)
- [x] ~fix caption so it will be shown for repetitive tracks~
- [x] ~some musics metadata is not shown~
- [ ] bot should send available tracks to users while new one is being downloaded with spotdl to use best of time.
- [ ] higher priority for first time users
- [x] ~only 1 single user can use the bot and it can't multitask~
- [ ] use a library like telethon for big mp3 files more than 50MB
- [x] ~searching in database algorithm isn't fast and efficient~
- [x] ~Download playlists with more thatn 100 songs~
- [ ] find a clean way to give access to database to next bot maintainers
- [ ] merge database of all spotify downloaders together
- [x] ~showing message to user when link from other services like deezer is sent.~
- [ ] find a way to shorten database (audio IDs are very long)
- [x] if all track_ids that a user wants already exists bypass normall routine and send all of them to him
- [x] ~test `portalocker` funcion from `db_csv_append` separately~
- [x] handle blocked by user link
- [ ] manage too threads bug
- [x] ~regex should handle both http and https~
- [x] make `restart_spotseek.sh` work without reboot too
- [ ] restarting queue handler doesn't stop previous spotdl download so there might be an excessive mp3 file that might lead to creating wrong track
- [ ] add gif tutorial for bot in the start
- [ ] check out `zotify` capabilities
- [ ] lyrics
- [ ] send picture and info of link
- [ ] private playlist answer
- [ ] more features for premium users
- [ ] read track data without api key in similar way to https://spotify.detta.dev/

## disclaimer
This project is for personal learning, do not use it for illegal purposes. Artists can send their copyright claims to the developer.

## support and donate
### Give me energy with coffee:
- [BuyMeACoffee](https://www.buymeacoffee.com/Arashnm80) (🇺🇸 $)
- [Coffeete](https://www.coffeete.ir/Arashnm80) (🇮🇷 ريال)
### Continuous monthly support:
- [Patreon](https://www.patreon.com/Arashnm80) (🇺🇸 $)
- [HamiBash](https://hamibash.com/Arashnm80) (🇮🇷 ريال)