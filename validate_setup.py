#!/usr/bin/python3

"""
Validation script for WhatsApp Spot Seek Bot
This script checks if all dependencies and configurations are properly set up
"""

import os
import sys
import json
import subprocess

def check_python_dependencies():
    """Check if required Python packages are installed"""
    required_packages = [
        'spotipy',
        'requests',
        'mutagen',
        'pydub',
        'portalocker'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is missing")
            missing_packages.append(package)
    
    return missing_packages

def check_nodejs_dependencies():
    """Check if Node.js and npm are available"""
    try:
        # Check Node.js
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js is installed: {result.stdout.strip()}")
        else:
            print("✗ Node.js is not installed")
            return False
        
        # Check npm
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ npm is installed: {result.stdout.strip()}")
        else:
            print("✗ npm is not installed")
            return False
        
        # Check if package.json exists
        if os.path.exists('package.json'):
            print("✓ package.json exists")
            
            # Check if node_modules exists
            if os.path.exists('node_modules'):
                print("✓ Node.js dependencies are installed")
            else:
                print("✗ Node.js dependencies not installed. Run: npm install")
                return False
        else:
            print("✗ package.json not found")
            return False
        
        return True
        
    except FileNotFoundError:
        print("✗ Node.js/npm not found in PATH")
        return False

def check_directories():
    """Check if required directories exist"""
    required_dirs = [
        'whatsapp_output',
        'received_links',
        'received_links/track',
        'received_links/album',
        'received_links/playlist',
        'output'
    ]
    
    missing_dirs = []
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ Directory exists: {directory}")
        else:
            print(f"✗ Directory missing: {directory}")
            missing_dirs.append(directory)
    
    return missing_dirs

def check_configuration():
    """Check if configuration is properly set up"""
    try:
        # Import variables to check configuration
        sys.path.append('.')
        from variables import spotify_apps_list, warp_mode
        
        if spotify_apps_list and len(spotify_apps_list) > 0:
            print(f"✓ Spotify apps configured: {len(spotify_apps_list)} app(s)")
        else:
            print("✗ No Spotify apps configured")
            return False
        
        print(f"✓ WARP mode: {'enabled' if warp_mode else 'disabled'}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Error importing configuration: {e}")
        return False
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def check_files():
    """Check if required files exist"""
    required_files = [
        'whatsapp_bot.js',
        'whatsapp_queue_handler.py',
        'process_spotify_link.py',
        'package.json',
        'variables.py',
        'functions.py',
        'spotify.py',
        'csv_functions.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ File exists: {file}")
        else:
            print(f"✗ File missing: {file}")
            missing_files.append(file)
    
    return missing_files

def main():
    print("WhatsApp Spot Seek Bot Setup Validation")
    print("=" * 40)
    
    all_good = True
    
    # Check files
    print("\n1. Checking required files...")
    missing_files = check_files()
    if missing_files:
        all_good = False
    
    # Check directories
    print("\n2. Checking directories...")
    missing_dirs = check_directories()
    if missing_dirs:
        print("Creating missing directories...")
        for directory in missing_dirs:
            os.makedirs(directory, exist_ok=True)
            print(f"Created: {directory}")
    
    # Check Python dependencies
    print("\n3. Checking Python dependencies...")
    missing_packages = check_python_dependencies()
    if missing_packages:
        print(f"Install missing packages: pip install {' '.join(missing_packages)}")
        all_good = False
    
    # Check Node.js dependencies
    print("\n4. Checking Node.js dependencies...")
    if not check_nodejs_dependencies():
        all_good = False
    
    # Check configuration
    print("\n5. Checking configuration...")
    if not check_configuration():
        print("Run setup_environment.py to configure Spotify apps")
        all_good = False
    
    print("\n" + "=" * 40)
    if all_good:
        print("✓ All checks passed! The bot should be ready to run.")
        print("Start the bot with: npm start")
        print("Or use the startup script: ./start_whatsapp_bot.sh")
    else:
        print("✗ Some issues found. Please fix them before running the bot.")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
