import os
import time

def log(log_message):
    """Simple file-based logging for WhatsApp bot"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {log_message}\n"

    try:
        with open("whatsapp_bot.log", "a", encoding="utf-8") as log_file:
            log_file.write(log_entry)
        print(f"[{timestamp}] {log_message}")
    except Exception as e:
        print(f"Error writing to log: {e}")
        print(f"[{timestamp}] {log_message}")
