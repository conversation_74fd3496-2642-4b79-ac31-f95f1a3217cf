#!/usr/bin/python3

import sys
import os
import json
import time
from pathlib import Path

# Import existing modules (but avoid Telegram dependencies)
from spotify import get_track_ids, get_link_type, get_redirect_link, get_valid_spotify_links
from functions import download, file_list, clear_files
from csv_functions import get_telegram_audio_id
from queue_functions import list_of_files_in_a_folder, read_list_from_file, write_list_to_file

# WhatsApp-specific configuration
WHATSAPP_OUTPUT_DIR = "./whatsapp_output/"
RECEIVED_LINKS_FOLDER = "./received_links"
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB

def ensure_directories():
    """Ensure required directories exist"""
    os.makedirs(WHATSAPP_OUTPUT_DIR, exist_ok=True)
    os.makedirs(f"{RECEIVED_LINKS_FOLDER}/track", exist_ok=True)
    os.makedirs(f"{RECEIVED_LINKS_FOLDER}/album", exist_ok=True)
    os.makedirs(f"{RECEIVED_LINKS_FOLDER}/playlist", exist_ok=True)

def log_whatsapp(message):
    """Simple logging for WhatsApp bot"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] WhatsApp Bot: {message}")

def check_file_size(file_path):
    """Check if file size is within WhatsApp limits"""
    try:
        file_size = os.path.getsize(file_path)
        return file_size <= MAX_FILE_SIZE, file_size
    except OSError:
        return False, 0

def process_single_track(track_id, user_id):
    """Process a single track for WhatsApp"""
    try:
        # Check if track exists in database first
        telegram_audio_id = get_telegram_audio_id(track_id)
        
        if telegram_audio_id is not None:
            log_whatsapp(f"Track {track_id} found in database for user {user_id}")
            return {"status": "found_in_db", "track_id": track_id}
        
        # Download the track
        track_url = f"https://open.spotify.com/track/{track_id}"
        log_whatsapp(f"Downloading track {track_id} for user {user_id}")
        
        # Clear output directory
        clear_files("./output/")
        
        # Download track
        download(track_url)
        
        # Get downloaded files
        files = file_list("./output/")
        
        if not files:
            log_whatsapp(f"No files downloaded for track {track_id}")
            return {"status": "error", "message": "No files downloaded"}
        
        # Move file to WhatsApp output directory with size check
        source_file = f"./output/{files[0]}"
        is_valid_size, file_size = check_file_size(source_file)
        
        if not is_valid_size:
            log_whatsapp(f"File too large: {file_size} bytes for track {track_id}")
            return {"status": "file_too_large", "size": file_size}
        
        # Create unique filename for WhatsApp
        dest_file = f"{WHATSAPP_OUTPUT_DIR}{user_id}_{track_id}.mp3"
        os.rename(source_file, dest_file)
        
        log_whatsapp(f"Track {track_id} ready for WhatsApp: {dest_file}")
        return {"status": "ready", "file_path": dest_file, "size": file_size}
        
    except Exception as e:
        log_whatsapp(f"Error processing track {track_id}: {str(e)}")
        return {"status": "error", "message": str(e)}

def process_spotify_link(link, user_id, link_type):
    """Main function to process Spotify links for WhatsApp"""
    try:
        ensure_directories()
        
        log_whatsapp(f"Processing {link_type} link from user {user_id}: {link}")
        
        # Handle shortened links
        if link_type == "shortened":
            link = get_redirect_link(link)
            link_type = get_link_type(link)
            if link_type not in ["track", "album", "playlist"]:
                return {"status": "error", "message": "Invalid shortened link"}
        
        # Get track IDs from the link
        track_ids = get_track_ids(link)
        
        if not track_ids:
            return {"status": "error", "message": "No tracks found in link"}
        
        # Limit tracks for large playlists
        if len(track_ids) > 1000:
            return {"status": "error", "message": "Playlist too large (max 1000 tracks)"}
        
        # Check if user already has something in queue
        for folder in ["track", "album", "playlist"]:
            folder_path = f"{RECEIVED_LINKS_FOLDER}/{folder}"
            files = list_of_files_in_a_folder(folder_path)
            if user_id in files:
                return {"status": "error", "message": "User already has download in progress"}
        
        results = []
        processed_count = 0
        max_immediate_process = 5  # Process max 5 tracks immediately
        
        # Process tracks immediately if they're in database or for small requests
        for track_id in track_ids[:max_immediate_process]:
            result = process_single_track(track_id, user_id)
            results.append(result)
            processed_count += 1
            
            if result["status"] not in ["found_in_db", "ready"]:
                break
        
        # If there are remaining tracks, add them to queue
        remaining_tracks = track_ids[processed_count:]
        if remaining_tracks:
            queue_file = f"{RECEIVED_LINKS_FOLDER}/{link_type}/{user_id}"
            write_list_to_file(remaining_tracks, queue_file)
            log_whatsapp(f"Added {len(remaining_tracks)} tracks to queue for user {user_id}")
        
        return {
            "status": "success",
            "processed": results,
            "queued": len(remaining_tracks),
            "total": len(track_ids)
        }
        
    except Exception as e:
        log_whatsapp(f"Error processing Spotify link: {str(e)}")
        return {"status": "error", "message": str(e)}

def main():
    """Main entry point for the script"""
    if len(sys.argv) != 4:
        print("Usage: python3 process_spotify_link.py <link> <user_id> <link_type>")
        sys.exit(1)
    
    link = sys.argv[1]
    user_id = sys.argv[2]
    link_type = sys.argv[3]
    
    result = process_spotify_link(link, user_id, link_type)
    print(json.dumps(result))

if __name__ == "__main__":
    main()
