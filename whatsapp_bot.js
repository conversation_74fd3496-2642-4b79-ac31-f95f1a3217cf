const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

// Initialize WhatsApp client with persistent authentication
const client = new Client({
    authStrategy: new LocalAuth({
        clientId: "spotseek_whatsapp_bot",
        dataPath: "./wwebjs_auth_session"
    }),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ]
    }
});

// Bot configuration
const BOT_NAME = "Spot Seek WhatsApp Bot";
const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB in bytes

// Spotify link patterns (copied from variables.py)
const SPOTIFY_PATTERNS = {
    shortened: /https?:\/\/spotify\.link\/[A-Za-z0-9]+/,
    track: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?track\/[a-zA-Z0-9]+/,
    album: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?album\/[a-zA-Z0-9]+/,
    playlist: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?playlist\/[a-zA-Z0-9]+/,
    episode: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?episode\/[a-zA-Z0-9]+/,
    artist: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?artist\/[a-zA-Z0-9]+/,
    user: /https?:\/\/open\.spotify\.com\/(intl-[a-zA-Z]{2}\/)?user\/[a-zA-Z0-9]+/
};

const OTHER_PATTERNS = {
    deezer: /https?:\/\/(?:www\.)?deezer\.com\/(?:\w{2}\/)?(?:\w+\/)?(?:track|album|artist|playlist)\/\d+/,
    soundcloud: /(?:https?:\/\/)?(?:www\.)?soundcloud\.com\/([a-zA-Z0-9-_]+)\/([a-zA-Z0-9-_]+)/,
    youtube: /(?:(?:https?:)?\/\/)?(?:www\.)?(?:(?:youtube\.com\/(?:watch\?.*v=|embed\/|v\/)|youtu.be\/))([\\w-]{11})/
};

// Messages
const MESSAGES = {
    welcome: `Hi😃👋
Send me a link from spotify and I'll download it for you.

some example links 👇

♪ track (very fast download)
https://open.spotify.com/track/734dz1YaFITwawPpM25fSt

🎵 album (fast download)
https://open.spotify.com/album/0Lg1uZvI312TPqxNWShFXL

🎶 playlist (normal download)
https://open.spotify.com/playlist/37i9dQZF1DWX4UlFW6EJPs`,

    info: `This bot's whole open source is available in github and all interested programmers are welcome to contribute and improve it.

Note: albums are downloaded faster than playlists and tracks are downloaded faster than albums.

You can support and motivate me to buy more servers for faster download by:
• Giving a star in github⭐🙂
• Or subscribing to my youtube🔥❤️`,

    privacy: `• This bot doesn't gather any info from the users
• Artists can send their copyright claims to the developer
• Bot's open source is available in github for educational purposes`,

    wrongLink: `This is not a correct spotify link.

You should send a track link like:
https://open.spotify.com/track/734dz1YaFITwawPpM25fSt

Or an album link like:
https://open.spotify.com/album/0Lg1uZvI312TPqxNWShFXL

Or a playlist link like:
https://open.spotify.com/playlist/37i9dQZF1DWX4UlFW6EJPs`,

    processing: "Ok🙂👍\nPlease be patient and wait till I download all of your link.\n\nYou will get a message in the end.",
    
    fileTooLarge: "⚠️ File too large for WhatsApp (limit 100MB)",
    
    end: `end.

Give me a star in Github⭐😉
Subscribe to My YouTube for more🔥`,

    deezer: `This bot is created to download from spotify but you sent a deezer link.
Send the link of your track/album/playlist from spotify`,

    soundcloud: `This bot is created to download from spotify but you sent a soundcloud link.
Send the link of your track/album/playlist from spotify`,

    youtube: `This bot is created to download from spotify but you sent a youtube link.
Send the link of your track/album/playlist from spotify`,

    episode: `You can't send podcast episode links.
Send the link of your track/album/playlist from spotify`,

    artist: `You can't send artist links.
Send the link of your track/album/playlist from spotify`,

    user: `You can't send user links.
Send the link of your track/album/playlist from spotify`
};

// QR Code generation for authentication
client.on('qr', (qr) => {
    console.log('QR Code received, scan with your phone:');
    qrcode.generate(qr, { small: true });
});

// Client ready event
client.on('ready', () => {
    console.log(`${BOT_NAME} is ready!`);

    // Start notification processing loop
    setInterval(processNotifications, 2000); // Check every 2 seconds
});

// Authentication events
client.on('authenticated', () => {
    console.log('WhatsApp client authenticated successfully');
});

client.on('auth_failure', (msg) => {
    console.error('Authentication failed:', msg);
});

// Utility functions
function getSpotifyLinkType(text) {
    if (SPOTIFY_PATTERNS.track.test(text)) return 'track';
    if (SPOTIFY_PATTERNS.album.test(text)) return 'album';
    if (SPOTIFY_PATTERNS.playlist.test(text)) return 'playlist';
    if (SPOTIFY_PATTERNS.shortened.test(text)) return 'shortened';
    if (SPOTIFY_PATTERNS.episode.test(text)) return 'episode';
    if (SPOTIFY_PATTERNS.artist.test(text)) return 'artist';
    if (SPOTIFY_PATTERNS.user.test(text)) return 'user';
    return null;
}

function getOtherLinkType(text) {
    if (OTHER_PATTERNS.deezer.test(text)) return 'deezer';
    if (OTHER_PATTERNS.soundcloud.test(text)) return 'soundcloud';
    if (OTHER_PATTERNS.youtube.test(text)) return 'youtube';
    return null;
}

async function checkFileSize(filePath) {
    try {
        const stats = await fs.stat(filePath);
        return stats.size;
    } catch (error) {
        console.error('Error checking file size:', error);
        return 0;
    }
}

async function sendDocumentWithSizeCheck(chat, filePath, caption = '') {
    const fileSize = await checkFileSize(filePath);
    
    if (fileSize > MAX_FILE_SIZE) {
        await chat.sendMessage(MESSAGES.fileTooLarge);
        return false;
    }
    
    try {
        const media = MessageMedia.fromFilePath(filePath);
        await chat.sendMessage(media, { caption, sendMediaAsDocument: true });
        return true;
    } catch (error) {
        console.error('Error sending document:', error);
        return false;
    }
}

// Call Python script to handle Spotify link processing
function processSpotifyLink(link, userId, linkType) {
    return new Promise((resolve, reject) => {
        const pythonProcess = spawn('python3', ['process_spotify_link.py', link, userId, linkType]);

        let output = '';
        let error = '';

        pythonProcess.stdout.on('data', (data) => {
            output += data.toString();
        });

        pythonProcess.stderr.on('data', (data) => {
            error += data.toString();
        });

        pythonProcess.on('close', (code) => {
            if (code === 0) {
                resolve(output.trim());
            } else {
                reject(new Error(`Python process exited with code ${code}: ${error}`));
            }
        });
    });
}

// Process notifications from queue handler
async function processNotifications() {
    const outputDir = './whatsapp_output/';

    try {
        if (!await fs.pathExists(outputDir)) {
            return;
        }

        const files = await fs.readdir(outputDir);
        const notificationFiles = files.filter(file => file.startsWith('notification_') && file.endsWith('.json'));

        for (const notificationFile of notificationFiles) {
            try {
                const filePath = path.join(outputDir, notificationFile);
                const notification = await fs.readJson(filePath);

                await handleNotification(notification);

                // Delete processed notification
                await fs.remove(filePath);

            } catch (error) {
                console.error(`Error processing notification ${notificationFile}:`, error);
            }
        }
    } catch (error) {
        console.error('Error processing notifications:', error);
    }
}

// Handle individual notifications
async function handleNotification(notification) {
    const { user_id, type, data } = notification;

    try {
        const chat = await client.getChatById(user_id);

        switch (type) {
            case 'track_ready':
                await sendTrackFile(chat, data);
                break;

            case 'file_too_large':
                await chat.sendMessage(MESSAGES.fileTooLarge);
                break;

            case 'queue_complete':
                await chat.sendMessage(MESSAGES.end);
                break;

            case 'error':
                await chat.sendMessage('Sorry, an error occurred while processing your request.');
                break;

            default:
                console.log(`Unknown notification type: ${type}`);
        }
    } catch (error) {
        console.error(`Error handling notification for user ${user_id}:`, error);
    }
}

// Send track file to user
async function sendTrackFile(chat, data) {
    const { file_path, cover_path, metadata } = data;

    try {
        if (!await fs.pathExists(file_path)) {
            console.error(`Track file not found: ${file_path}`);
            return;
        }

        const media = MessageMedia.fromFilePath(file_path);

        // Create caption with metadata
        let caption = '';
        if (metadata) {
            caption = `🎵 ${metadata.title}\n👤 ${metadata.artist}`;
        }

        await chat.sendMessage(media, {
            caption,
            sendMediaAsDocument: true
        });

        // Clean up the file after sending
        await fs.remove(file_path);
        if (cover_path && await fs.pathExists(cover_path)) {
            await fs.remove(cover_path);
        }

    } catch (error) {
        console.error('Error sending track file:', error);
        await chat.sendMessage('Sorry, there was an error sending your track.');
    }
}

// Message handler
client.on('message', async (message) => {
    const chat = await message.getChat();
    const messageBody = message.body.trim();
    const userId = message.from;
    
    console.log(`Message from ${userId}: ${messageBody}`);
    
    // Handle commands
    if (messageBody.toLowerCase() === '/start' || messageBody.toLowerCase() === 'start') {
        await chat.sendMessage(MESSAGES.welcome);
        return;
    }
    
    if (messageBody.toLowerCase() === '/info' || messageBody.toLowerCase() === 'info') {
        await chat.sendMessage(MESSAGES.info);
        return;
    }
    
    if (messageBody.toLowerCase() === '/privacy' || messageBody.toLowerCase() === 'privacy') {
        await chat.sendMessage(MESSAGES.privacy);
        return;
    }
    
    // Check for other platform links first
    const otherLinkType = getOtherLinkType(messageBody);
    if (otherLinkType) {
        await chat.sendMessage(MESSAGES[otherLinkType]);
        return;
    }
    
    // Check for Spotify links
    const spotifyLinkType = getSpotifyLinkType(messageBody);
    if (spotifyLinkType) {
        // Handle unsupported Spotify link types
        if (['episode', 'artist', 'user'].includes(spotifyLinkType)) {
            await chat.sendMessage(MESSAGES[spotifyLinkType]);
            return;
        }
        
        // Handle supported Spotify links
        if (['track', 'album', 'playlist', 'shortened'].includes(spotifyLinkType)) {
            await chat.sendMessage(MESSAGES.processing);

            try {
                // Call Python script to process the Spotify link
                const resultStr = await processSpotifyLink(messageBody, userId, spotifyLinkType);
                const result = JSON.parse(resultStr);

                console.log('Python processing result:', result);

                if (result.status === 'success') {
                    // Process immediate results
                    for (const trackResult of result.processed) {
                        if (trackResult.status === 'ready') {
                            // File is ready, send it
                            await sendTrackFile(chat, trackResult);
                        } else if (trackResult.status === 'file_too_large') {
                            await chat.sendMessage(MESSAGES.fileTooLarge);
                        }
                    }

                    // If tracks were queued, inform user
                    if (result.queued > 0) {
                        await chat.sendMessage(`Processing ${result.queued} more tracks. You'll receive them shortly.`);
                    } else if (result.processed.length > 0) {
                        // All tracks processed immediately
                        await chat.sendMessage(MESSAGES.end);
                    }
                } else {
                    // Handle errors
                    if (result.message.includes('already has download')) {
                        await chat.sendMessage('You already have a download in progress. Please wait for it to complete.');
                    } else if (result.message.includes('too large')) {
                        await chat.sendMessage("Bot can't download playlists more than 1000 tracks at the moment.");
                    } else {
                        await chat.sendMessage('Sorry, there was an error processing your link. Please try again.');
                    }
                }

            } catch (error) {
                console.error('Error processing Spotify link:', error);
                await chat.sendMessage('Sorry, an error occurred while processing your link. Please try again later.');
            }
            return;
        }
    }
    
    // Default response for unrecognized messages
    await chat.sendMessage(MESSAGES.wrongLink);
});

// Initialize the client
client.initialize();

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down WhatsApp bot...');
    await client.destroy();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Shutting down WhatsApp bot...');
    await client.destroy();
    process.exit(0);
});
