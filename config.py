# WhatsApp Spot Seek Bot Configuration
# Edit this file with your settings

# Spotify App Credentials
# Get these from https://developer.spotify.com/
# Format: [["client_id_1", "client_secret_1"], ["client_id_2", "client_secret_2"]]
SPOTIFY_APPS = [
    # Add your Spotify app credentials here
    # Example: ["your_client_id", "your_client_secret"]
    ["1ef8de040c8a490ba39930551d655944", "66e0672bc9ea437c8bfb682ff18c7d8e"]  # Replace with real credentials
]

# WARP/Proxy Settings
USE_WARP = False  # Set to True if you want to use WARP proxy

# WARP Proxy Configuration (only used if USE_WARP is True)
WARP_PROXIES = {
    "http": "socks5://127.0.0.1:40000",
    "https": "socks5://127.0.0.1:40000"
}

# Bot Settings
BOT_NAME = "Spot Seek WhatsApp Bot"
BOT_USERNAME = "SpotSeekBot"

# Download Settings
PLAYLIST_DOWNLOAD_RATE = 0.5  # 0.5 means download 50% of playlist tracks (to manage load)
QUEUE_HANDLER_MAX_FORWARDS = 10  # Max tracks to process in one cycle

# File Paths
OUTPUT_DIRECTORY = "./output/"
WHATSAPP_OUTPUT_DIRECTORY = "./whatsapp_output/"
RECEIVED_LINKS_FOLDER = "./received_links"
SPOTDL_CACHE_PATH = "/root/.spotdl"  # Linux path, adjust for Windows if needed

# SpotDL Settings
SPOTDL_EXECUTABLE_LINK = "https://github.com/spotDL/spotify-downloader/releases/download/v4.2.11/spotdl-4.2.11-linux"

# Database Settings
DB_TIME_COLUMN = 0
DB_SP_TRACK_COLUMN = 1
DB_TL_AUDIO_COLUMN = 2

# User CSV Settings
UCSV_USER_ID_COLUMN = 0
UCSV_LAST_TIME_COLUMN = 1

# Date/Time Format
DATETIME_FORMAT = "%Y/%m/%d-%H:%M:%S"

# User Request Settings
USER_REQUEST_WAIT = 30  # Seconds between requests from same user
