#!/bin/bash

# Start WhatsApp Spot Seek Bot
echo "Starting WhatsApp Spot Seek Bot..."

# Create necessary directories
mkdir -p whatsapp_output
mkdir -p received_links/track
mkdir -p received_links/album
mkdir -p received_links/playlist

# Install Node.js dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing Node.js dependencies..."
    npm install
fi

# Start the WhatsApp queue handler in background
echo "Starting WhatsApp queue handler..."
nohup python3 whatsapp_queue_handler.py > whatsapp_queue_handler.log 2>&1 &
echo $! > whatsapp_queue_handler.pid

# Start the WhatsApp bot
echo "Starting WhatsApp bot..."
npm start
