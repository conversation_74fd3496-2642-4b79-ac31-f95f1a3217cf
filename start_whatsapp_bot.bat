@echo off
echo Starting WhatsApp Spot Seek Bot...

REM Create necessary directories
if not exist "whatsapp_output" mkdir whatsapp_output
if not exist "received_links" mkdir received_links
if not exist "received_links\track" mkdir received_links\track
if not exist "received_links\album" mkdir received_links\album
if not exist "received_links\playlist" mkdir received_links\playlist

REM Install Node.js dependencies if needed
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
)

REM Start the WhatsApp queue handler in background
echo Starting WhatsApp queue handler...
start /B python whatsapp_queue_handler.py

REM Start the WhatsApp bot
echo Starting WhatsApp bot...
npm start
