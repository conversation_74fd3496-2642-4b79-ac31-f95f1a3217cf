#!/bin/bash

# Stop WhatsApp Spot Seek Bot
echo "Stopping WhatsApp Spot Seek Bot..."

# Stop the queue handler if running
if [ -f whatsapp_queue_handler.pid ]; then
    PID=$(cat whatsapp_queue_handler.pid)
    if ps -p $PID > /dev/null; then
        echo "Stopping queue handler (PID: $PID)..."
        kill $PID
        rm whatsapp_queue_handler.pid
    else
        echo "Queue handler not running"
        rm whatsapp_queue_handler.pid
    fi
else
    echo "No queue handler PID file found"
fi

# Kill any remaining Python processes
pkill -f "whatsapp_queue_handler.py"

# Kill any remaining Node.js processes for this bot
pkill -f "whatsapp_bot.js"

echo "WhatsApp bot stopped"
