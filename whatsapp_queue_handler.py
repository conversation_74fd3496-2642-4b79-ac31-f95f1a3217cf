#!/usr/bin/python3

import os
import time
import json
import subprocess
from pathlib import Path

# Import existing modules (avoiding Telegram dependencies)
from queue_functions import list_of_files_in_a_folder, read_list_from_file, write_list_to_file
from csv_functions import get_telegram_audio_id, add_or_update_track_info
from functions import download, file_list, clear_files, delete_spotdl_cache, setup_spotdl_executable
from mp3 import change_cover_image, get_track_duration, get_artist_name_from_track, get_track_title
from spotify import get_track_image

# WhatsApp-specific configuration
WHATSAPP_OUTPUT_DIR = "./whatsapp_output/"
RECEIVED_LINKS_FOLDER = "./received_links"
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
QUEUE_HANDLER_MAX_FORWARDS = 10
PLAYLIST_DOWNLOAD_RATE = 0.5

def log_whatsapp(message):
    """Simple logging for WhatsApp bot"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] WhatsApp Queue Handler: {message}")

def ensure_directories():
    """Ensure required directories exist"""
    os.makedirs(WHATSAPP_OUTPUT_DIR, exist_ok=True)

def check_file_size(file_path):
    """Check if file size is within WhatsApp limits"""
    try:
        file_size = os.path.getsize(file_path)
        return file_size <= MAX_FILE_SIZE, file_size
    except OSError:
        return False, 0

def notify_whatsapp_bot(user_id, message_type, data=None):
    """Send notification to WhatsApp bot via file system"""
    notification = {
        "user_id": user_id,
        "type": message_type,
        "data": data,
        "timestamp": time.time()
    }
    
    notification_file = f"{WHATSAPP_OUTPUT_DIR}notification_{user_id}_{int(time.time())}.json"
    with open(notification_file, 'w') as f:
        json.dump(notification, f)

def handle_track_for_whatsapp_user(track_id, user_id, folder_type):
    """Handle track processing for WhatsApp users"""
    try:
        # Experimental - to see if has effect on spotdl rate limits
        delete_spotdl_cache()
        
        # Remove junk files from drive
        clear_files("./output/")
        
        # Check if track exists in database
        telegram_audio_id = get_telegram_audio_id(track_id)
        
        if telegram_audio_id is not None:
            # Track exists in database, create notification for WhatsApp bot
            notify_whatsapp_bot(user_id, "track_from_db", {
                "track_id": track_id,
                "audio_id": telegram_audio_id
            })
            return "forwarded"
        
        # File doesn't exist in database and should be downloaded
        
        # Experimental - randomly ignore download of some tracks for playlists
        import random
        random_number = round(random.random(), 2)
        if (folder_type == "playlist") and (random_number > PLAYLIST_DOWNLOAD_RATE):
            log_whatsapp(f"Track {track_id} skipped for user {user_id} (random: {random_number})")
            return "skipped"
        
        log_whatsapp(f"Processing track {track_id} for user {user_id}")
        
        # Download the track
        link = f"https://open.spotify.com/track/{track_id}"
        download(link)
        
        # Process downloaded files
        files = file_list("./output/")
        
        if not files:
            log_whatsapp(f"No files downloaded for track {track_id}")
            return "noFileSentError"
        
        for file in files:
            log_whatsapp(f"Processing downloaded file: {file}")
            
            # Change cover image
            change_cover_image(file, "cover.jpg")
            
            # Check file size
            file_path = f"./output/{file}"
            is_valid_size, file_size = check_file_size(file_path)
            
            if not is_valid_size:
                log_whatsapp(f"File too large: {file_size} bytes for track {track_id}")
                notify_whatsapp_bot(user_id, "file_too_large", {
                    "track_id": track_id,
                    "file_size": file_size
                })
                return "largeMp3Error"
            
            # Get track metadata
            track_duration = get_track_duration(f"./output/{file}")
            track_artist = get_artist_name_from_track(f"./output/{file}")
            track_title = get_track_title(f"./output/{file}")
            
            # Move file to WhatsApp output directory
            dest_file = f"{WHATSAPP_OUTPUT_DIR}{user_id}_{track_id}.mp3"
            os.rename(file_path, dest_file)
            
            # Copy cover image
            cover_dest = f"{WHATSAPP_OUTPUT_DIR}{user_id}_{track_id}_cover.jpg"
            if os.path.exists("./output/cover_low.jpg"):
                os.rename("./output/cover_low.jpg", cover_dest)
            
            # Add to database (using existing system)
            # Note: We'll use a placeholder for telegram_audio_id since we don't have Telegram
            add_or_update_track_info(track_id, f"whatsapp_{track_id}")
            
            # Notify WhatsApp bot
            notify_whatsapp_bot(user_id, "track_ready", {
                "track_id": track_id,
                "file_path": dest_file,
                "cover_path": cover_dest if os.path.exists(cover_dest) else None,
                "metadata": {
                    "title": track_title,
                    "artist": track_artist,
                    "duration": track_duration
                }
            })
            
            # Clean up
            clear_files("./output/")
            
            return "downloaded"
        
        return "noFileSentError"
        
    except Exception as e:
        log_whatsapp(f"Error handling track {track_id} for user {user_id}: {str(e)}")
        notify_whatsapp_bot(user_id, "error", {
            "track_id": track_id,
            "error": str(e)
        })
        return "otherUncheckedError"

def main():
    """Main queue handler loop"""
    try:
        ensure_directories()
        log_whatsapp("Queue handler started")
        
        # Setup spotdl executable
        setup_spotdl_executable()
        
        for folder in ["track", "album", "playlist"]:
            folder_path = f"{RECEIVED_LINKS_FOLDER}/{folder}"
            
            if not os.path.exists(folder_path):
                continue
                
            files = list_of_files_in_a_folder(folder_path)
            
            # Process each user's queue
            for user_id in files:
                file_path = f"{folder_path}/{user_id}"
                tracks = read_list_from_file(file_path)
                
                # If file is empty, delete it and continue
                if not tracks:
                    os.remove(file_path)
                    log_whatsapp(f"Deleted empty queue file for user {user_id}")
                    continue
                
                consecutive_download = 0
                
                # Process tracks for this user
                while tracks:
                    first_track = tracks.pop(0)
                    consecutive_download += 1
                    
                    track_handling_result = handle_track_for_whatsapp_user(
                        first_track, user_id, folder
                    )
                    
                    # Break if we've processed enough or hit an error
                    if ((track_handling_result not in ["forwarded", "skipped"]) or 
                        (consecutive_download >= QUEUE_HANDLER_MAX_FORWARDS)):
                        break
                    else:
                        time.sleep(0.1)  # Small delay to avoid overwhelming
                
                # Handle remaining tracks
                if tracks:
                    # Write remaining tracks back to file
                    write_list_to_file(tracks, file_path)
                else:
                    # All tracks processed, delete file and notify completion
                    os.remove(file_path)
                    notify_whatsapp_bot(user_id, "queue_complete", {})
                    log_whatsapp(f"Completed queue for user {user_id}")
        
        log_whatsapp("Queue handler cycle completed")
        
    except Exception as e:
        log_whatsapp(f"Error in queue handler: {str(e)}")
    
    # Schedule next run
    try:
        sleep_time = 5
        command = f"sleep {sleep_time} && nohup python3 ./whatsapp_queue_handler.py > /dev/null 2>&1 &"
        subprocess.Popen(command, shell=True, close_fds=True)
        log_whatsapp(f"Scheduled next run in {sleep_time} seconds")
    except subprocess.CalledProcessError as e:
        log_whatsapp(f"Error scheduling next run: {e}")

if __name__ == "__main__":
    main()
