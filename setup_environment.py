#!/usr/bin/python3

"""
Environment setup script for WhatsApp Spot Seek Bot
This script helps configure the necessary environment variables and settings
"""

import os
import json

def setup_spotify_apps():
    """Setup Spotify app credentials"""
    print("Setting up Spotify app credentials...")
    print("You need to create a Spotify app at https://developer.spotify.com/")
    print("and get your Client ID and Client Secret")
    
    apps = []
    while True:
        client_id = input("Enter Spotify Client ID (or 'done' to finish): ").strip()
        if client_id.lower() == 'done':
            break
        
        client_secret = input("Enter Spotify Client Secret: ").strip()
        
        if client_id and client_secret:
            apps.append([client_id, client_secret])
            print(f"Added Spotify app {len(apps)}")
        else:
            print("Invalid credentials, please try again")
    
    return apps

def setup_warp_proxy():
    """Setup WARP proxy configuration"""
    use_warp = input("Do you want to use WARP proxy? (y/n): ").strip().lower()
    
    if use_warp == 'y':
        print("Please configure your WARP proxy in /etc/proxychains4.conf")
        print("Example configuration:")
        print("socks5 127.0.0.1 40000")
        
        proxy_config = {
            "http": "socks5://127.0.0.1:40000",
            "https": "socks5://127.0.0.1:40000"
        }
        return True, proxy_config
    else:
        return False, {}

def update_variables_file(spotify_apps, warp_mode, warp_proxies):
    """Update variables.py with new configuration"""
    
    # Read current variables.py
    with open('variables.py', 'r') as f:
        content = f.read()
    
    # Update spotify apps list
    spotify_apps_str = json.dumps(spotify_apps)
    
    # Find and replace spotify_apps_list
    import re
    
    # Replace spotify_apps_list
    pattern = r'spotify_apps_list = os\.environ\["SPOTIFY_APPS_LIST"\]\nspotify_apps_list = json\.loads\(spotify_apps_list\)'
    replacement = f'spotify_apps_list = {spotify_apps_str}'
    content = re.sub(pattern, replacement, content)
    
    # Replace warp_mode
    content = re.sub(r'warp_mode = True', f'warp_mode = {warp_mode}', content)
    
    # Replace warp_proxies if warp is enabled
    if warp_mode:
        warp_proxies_str = json.dumps(warp_proxies)
        pattern = r'warp_proxies = os\.environ\["WARP_PROXIES"\]\nwarp_proxies = json\.loads\(warp_proxies\)'
        replacement = f'warp_proxies = {warp_proxies_str}'
        content = re.sub(pattern, replacement, content)
    
    # Write updated content
    with open('variables.py', 'w') as f:
        f.write(content)
    
    print("Updated variables.py with new configuration")

def create_directories():
    """Create necessary directories"""
    directories = [
        'whatsapp_output',
        'received_links/track',
        'received_links/album',
        'received_links/playlist',
        'output'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def main():
    print("WhatsApp Spot Seek Bot Environment Setup")
    print("=" * 40)
    
    # Setup Spotify apps
    spotify_apps = setup_spotify_apps()
    
    if not spotify_apps:
        print("Error: At least one Spotify app is required!")
        return
    
    # Setup WARP proxy
    warp_mode, warp_proxies = setup_warp_proxy()
    
    # Update variables.py
    try:
        update_variables_file(spotify_apps, warp_mode, warp_proxies)
    except Exception as e:
        print(f"Error updating variables.py: {e}")
        print("Please manually update the file with your Spotify credentials")
    
    # Create directories
    create_directories()
    
    print("\nSetup completed!")
    print("Next steps:")
    print("1. Install dependencies: pip install -r requirements.txt && npm install")
    print("2. Install ffmpeg: sudo apt install ffmpeg")
    print("3. Run the bot: ./start_whatsapp_bot.sh")
    print("4. Scan the QR code with your WhatsApp on first run")

if __name__ == "__main__":
    main()
